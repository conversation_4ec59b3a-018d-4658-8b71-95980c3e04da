<?php

declare(strict_types=1);

use App\Modules\SavedItems\Http\Controllers\SavedItemsController;
use Illuminate\Support\Facades\Route;

Route::middleware(['api'])->prefix('api')->group(function () {
    Route::middleware(['auth:sanctum', 'header:highfive-clinic'])->prefix('saved-items')->group(function () {
        Route::get('/', [SavedItemsController::class, 'index']);
        Route::post('/add', [SavedItemsController::class, 'addToSavedItems']);
        Route::post('/add-to-cart', [SavedItemsController::class, 'addToCartFromSavedItems']);
        Route::delete('/remove', [SavedItemsController::class, 'removeFromSavedItems']);
    });
});
