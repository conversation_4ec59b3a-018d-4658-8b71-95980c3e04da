<?php

declare(strict_types=1);

namespace App\Modules\SavedItems\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

final class RemoveFromSavedItemsRequest extends FormRequest
{
    public function authorize(): bool
    {
        return $this->user()->account->clinics->contains(fn ($clinic) => $clinic->id === $this->clinicId());
    }

    public function rules(): array
    {
        $clinicId = $this->clinicId();

        return [
            'saved_item_id' => 'required_without:saved_item_ids|string|uuid|exists:saved_items,id,clinic_id,'.$clinicId,
            'saved_item_ids' => 'required_without:saved_item_id|array|min:1',
            'saved_item_ids.*' => 'required|string|uuid|exists:saved_items,id,clinic_id,'.$clinicId,
        ];
    }

    public function messages(): array
    {
        return [
            'saved_item_id.required_without' => 'Either saved_item_id or saved_item_ids is required.',
            'saved_item_id.uuid' => 'Saved item ID must be a valid UUID.',
            'saved_item_id.exists' => 'The specified saved item does not exist.',
            'saved_item_ids.required_without' => 'Either saved_item_id or saved_item_ids is required.',
            'saved_item_ids.array' => 'Saved item IDs must be an array.',
            'saved_item_ids.min' => 'At least one saved item ID is required.',
            'saved_item_ids.*.required' => 'Each saved item ID is required.',
            'saved_item_ids.*.uuid' => 'Each saved item ID must be a valid UUID.',
            'saved_item_ids.*.exists' => 'One or more saved items do not exist or do not belong to your clinic.',
        ];
    }

    /**
     * Get the saved item IDs from the request, handling both single and array formats.
     */
    public function getSavedItemIds(): array
    {
        if ($this->has('saved_item_ids')) {
            return $this->input('saved_item_ids');
        }

        if ($this->has('saved_item_id')) {
            return [$this->input('saved_item_id')];
        }

        return [];
    }
}
