<?php

declare(strict_types=1);

namespace App\Modules\SavedItems\Http\Requests;

use App\Models\CartItem;
use Illuminate\Foundation\Http\FormRequest;

final class AddToSavedItemsRequest extends FormRequest
{
    public function authorize(): bool
    {
        $clinicId = $this->clinicId();

        // Check if user has access to the clinic
        if (!$this->user()->account->clinics->contains(fn ($clinic) => $clinic->id === $clinicId)) {
            return false;
        }

        // Get cart item IDs to validate
        $cartItemIds = $this->input('cart_item_ids', []);

        if (empty($cartItemIds)) {
            return true; // Let validation rules handle empty array
        }

        // Validate that all cart items belong to the clinic
        $cartItems = CartItem::whereIn('id', $cartItemIds)->with('cart')->get();

        if ($cartItems->count() !== count($cartItemIds)) {
            return false; // Some cart items don't exist
        }

        return $cartItems->every(fn ($cartItem) => $cartItem->cart->clinic_id === $clinicId);
    }

    public function rules(): array
    {
        return [
            'cart_item_ids' => 'required|array|min:1',
            'cart_item_ids.*' => 'required|string|uuid|exists:cart_items,id',
        ];
    }

    public function messages(): array
    {
        return [
            'cart_item_ids.required' => 'Cart item IDs are required.',
            'cart_item_ids.array' => 'Cart item IDs must be an array.',
            'cart_item_ids.min' => 'At least one cart item ID is required.',
            'cart_item_ids.*.required' => 'Each cart item ID is required.',
            'cart_item_ids.*.uuid' => 'Each cart item ID must be a valid UUID.',
            'cart_item_ids.*.exists' => 'One or more cart items do not exist.',
        ];
    }
}
