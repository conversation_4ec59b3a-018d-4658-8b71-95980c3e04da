# SavedItems Module - Bulk Operations

## Overview

The SavedItems module has been enhanced to support bulk operations while maintaining backward compatibility with single-item operations. This allows for efficient processing of multiple items in a single API call with proper transaction safety.

## API Endpoints

### Add Items to Saved Items
**POST** `/api/saved-items/add`

#### Single Item (Backward Compatible)
```json
{
  "cart_item_id": "uuid-string"
}
```

#### Multiple Items (New)
```json
{
  "cart_item_ids": ["uuid-1", "uuid-2", "uuid-3"]
}
```

#### Response
- **Single Item**: Returns `SavedItemData` object
- **Multiple Items**: Returns object with `saved_items` array and `count`

### Remove Items from Saved Items
**DELETE** `/api/saved-items/remove`

#### Single Item (Backward Compatible)
```json
{
  "saved_item_id": "uuid-string"
}
```

#### Multiple Items (New)
```json
{
  "saved_item_ids": ["uuid-1", "uuid-2", "uuid-3"]
}
```

#### Response
- Always returns `204 No Content`

## Key Features

### 1. Backward Compatibility
- Existing frontend code using single `cart_item_id` or `saved_item_id` continues to work
- No breaking changes to existing API contracts

### 2. Quantity Accumulation
- When multiple cart items have the same product offer, quantities are automatically accumulated
- Example: 2 cart items with quantities 3 and 2 for the same product → 1 saved item with quantity 5

### 3. Transaction Safety
- All bulk operations are wrapped in database transactions
- If any item in the batch fails, the entire operation is rolled back
- Ensures data consistency

### 4. Validation
- All cart items must belong to the same clinic
- All saved items must belong to the same clinic
- Proper authorization checks for clinic access
- UUID validation for all IDs

### 5. Cache Management
- Cart cache is properly cleared after operations
- Ensures fresh data is loaded on subsequent requests

## Usage Examples

### Frontend Implementation

```typescript
// Single item (existing)
const response = await fetchApi('/saved-items/add', {
  method: 'POST',
  body: { cart_item_id: 'uuid' }
});

// Multiple items (new)
const response = await fetchApi('/saved-items/add', {
  method: 'POST',
  body: { cart_item_ids: ['uuid1', 'uuid2', 'uuid3'] }
});

// Bulk remove
const response = await fetchApi('/saved-items/remove', {
  method: 'DELETE',
  body: { saved_item_ids: ['uuid1', 'uuid2', 'uuid3'] }
});
```

### Backend Actions

```php
// Single item (existing)
$savedItem = $addToSavedItemsAction->handle($cartItemId);

// Multiple items (new)
$savedItems = $addToSavedItemsAction->handleBulk($cartItemIds);

// Bulk restore to cart
$savedItems = $addToCartFromSavedItemsAction->handleBulk($savedItemIds);
```

## Error Handling

### Validation Errors (422)
- Missing required fields
- Invalid UUID format
- Non-existent cart/saved items
- Items belonging to different clinics

### Authorization Errors (403)
- User doesn't have access to the clinic
- Cart items don't belong to user's clinic

### Server Errors (500)
- Database transaction failures
- Unexpected exceptions during processing

## Testing

Comprehensive tests have been added covering:
- Bulk operations for both directions (cart ↔ saved items)
- Quantity accumulation logic
- Transaction rollback scenarios
- API endpoint validation
- Backward compatibility
- Error conditions

Run tests with:
```bash
php artisan test tests/Feature/Modules/SavedItem/SavedItemTest.php
```

## Migration Notes

### For Frontend Developers
1. **No immediate changes required** - existing code continues to work
2. **To implement bulk operations**: Use `cart_item_ids` or `saved_item_ids` arrays instead of single IDs
3. **Handle different response formats**: Single items return object, multiple items return array with count

### For Backend Developers
1. **Actions support both single and bulk**: Use `handle()` for single, `handleBulk()` for multiple
2. **Transaction safety**: All bulk operations are automatically wrapped in transactions
3. **Validation**: Request classes handle both single and array validation automatically
