# SavedItems Module - Array-Only Operations

## Overview

The SavedItems module has been updated to support array-only operations for better consistency and bulk processing. All endpoints now require arrays of IDs, allowing for efficient processing of single or multiple items in a unified API approach with proper transaction safety.

## API Endpoints

### Add Items to Saved Items
**POST** `/api/saved-items/add`

#### Request Format
```json
{
  "cart_item_ids": ["uuid-1", "uuid-2", "uuid-3"]
}
```

#### Response
Returns object with `saved_items` array and `count`:
```json
{
  "saved_items": [
    {
      "id": "uuid",
      "clinic_id": "uuid",
      "product_offer_id": "uuid",
      "quantity": 5,
      "created_at": "2024-01-01T00:00:00Z",
      "product_offer": { ... }
    }
  ],
  "count": 1
}
```

### Remove Items from Saved Items
**DELETE** `/api/saved-items/remove`

#### Request Format
```json
{
  "saved_item_ids": ["uuid-1", "uuid-2", "uuid-3"]
}
```

#### Response
- Always returns `204 No Content`

## Key Features

### 1. Array-Only Approach
- All endpoints require arrays of IDs for consistency
- Single items are handled by passing arrays with one element
- Simplified API contract with unified response format

### 2. Individual Processing
- Each cart item is processed individually using the same logic as before
- If a saved item already exists for the same product offer, the cart item quantity is added to it
- Example: Cart item with quantity 3 + existing saved item with quantity 2 → saved item with quantity 5

### 3. Transaction Safety
- All operations are wrapped in database transactions
- If any item in the batch fails, the entire operation is rolled back
- Ensures data consistency

### 4. Validation
- Proper authorization checks for clinic access
- UUID validation for all IDs
- Arrays must contain at least one item
- Cart items and saved items are validated to belong to the authorized clinic

### 5. Cache Management
- Cart cache is properly cleared after operations
- Ensures fresh data is loaded on subsequent requests

## Usage Examples

### Frontend Implementation

```typescript
// Single item
const response = await fetchApi('/saved-items/add', {
  method: 'POST',
  body: { cart_item_ids: ['uuid'] }
});

// Multiple items
const response = await fetchApi('/saved-items/add', {
  method: 'POST',
  body: { cart_item_ids: ['uuid1', 'uuid2', 'uuid3'] }
});

// Remove items
const response = await fetchApi('/saved-items/remove', {
  method: 'DELETE',
  body: { saved_item_ids: ['uuid1', 'uuid2', 'uuid3'] }
});
```

### Backend Actions

```php
// Single or multiple items
$savedItems = $addToSavedItemsAction->handle($cartItemIds);

// Restore to cart
$savedItems = $addToCartFromSavedItemsAction->handle($savedItemIds);
```

## Error Handling

### Validation Errors (422)
- Missing required fields
- Invalid UUID format
- Non-existent cart/saved items
- Items belonging to different clinics

### Authorization Errors (403)
- User doesn't have access to the clinic
- Cart items don't belong to user's clinic

### Server Errors (500)
- Database transaction failures
- Unexpected exceptions during processing

## Testing

Comprehensive tests have been added covering:
- Bulk operations for both directions (cart ↔ saved items)
- Quantity accumulation logic
- Transaction rollback scenarios
- API endpoint validation
- Backward compatibility
- Error conditions

Run tests with:
```bash
php artisan test tests/Feature/Modules/SavedItem/SavedItemTest.php
```

## Migration Notes

### For Frontend Developers
1. **Breaking change**: Must update all calls to use arrays
   - Change `{ cart_item_id: 'uuid' }` to `{ cart_item_ids: ['uuid'] }`
   - Change `{ saved_item_id: 'uuid' }` to `{ saved_item_ids: ['uuid'] }`
2. **Response format**: All responses now return arrays with count
3. **Single items**: Pass arrays with one element for single item operations

### For Backend Developers
1. **Simplified actions**: Only `handle()` method exists, always takes arrays
2. **Transaction safety**: All operations are automatically wrapped in transactions
3. **Validation**: Request classes only accept arrays with minimum one item
