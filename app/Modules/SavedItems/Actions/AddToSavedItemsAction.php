<?php

declare(strict_types=1);

namespace App\Modules\SavedItems\Actions;

use App\Models\CartItem;
use App\Modules\Cart\Actions\GetCartAction;
use App\Modules\SavedItems\Models\SavedItem;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

final class AddToSavedItemsAction
{
    public function __construct(
        private readonly GetCartAction $getCartAction
    ) {}

    /**
     * Handle multiple cart item IDs in a single transaction.
     */
    public function handle(array $cartItemIds): Collection
    {
        return DB::transaction(function () use ($cartItemIds) {
            // Load all cart items with their relationships
            $cartItems = CartItem::with(['cart.clinic'])
                ->whereIn('id', $cartItemIds)
                ->get();

            $clinic = $cartItems->first()->cart->clinic;
            $savedItems = collect();

            // Process each cart item individually (same logic as before)
            foreach ($cartItems as $cartItem) {
                $productOffer = $cartItem->productOffer;
                $quantity = $cartItem->quantity;

                // Find or create saved item
                $savedItem = SavedItem::firstOrNew([
                    'clinic_id' => $clinic->id,
                    'product_offer_id' => $productOffer->id,
                ], [
                    'quantity' => 0,
                ]);

                // Use same logic as before - add to existing quantity if exists
                if ($savedItem->exists) {
                    $savedItem->quantity += $quantity;
                } else {
                    $savedItem->quantity = $quantity;
                }

                $savedItem->save();
                $savedItems->push($savedItem);
            }

            // Delete all cart items
            CartItem::whereIn('id', $cartItemIds)->delete();

            // Clear cart cache to ensure fresh data is loaded
            $this->getCartAction->forgetCache($clinic);

            return $savedItems;
        });
    }
}
