<?php

declare(strict_types=1);

namespace App\Modules\SavedItems\Actions;

use App\Models\CartItem;
use App\Modules\Cart\Actions\GetCartAction;
use App\Modules\SavedItems\Models\SavedItem;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;

final class AddToSavedItemsAction
{
    public function __construct(
        private readonly GetCartAction $getCartAction
    ) {}

    /**
     * Handle multiple cart item IDs.
     */
    public function handle(array $cartItemIds): Collection
    {
        // Load all cart items with their relationships
        $cartItems = CartItem::with(['cart.clinic'])
            ->whereIn('id', $cartItemIds)
            ->get();

        $savedItems = collect();
        $processedCartItemIds = collect();
        $clinic = null;

        // Process each cart item individually (same logic as before)
        foreach ($cartItems as $cartItem) {
            try {
                if (!$clinic) {
                    $clinic = $cartItem->cart->clinic;
                }

                $productOffer = $cartItem->productOffer;
                $quantity = $cartItem->quantity;

                // Find or create saved item
                $savedItem = SavedItem::firstOrNew([
                    'clinic_id' => $clinic->id,
                    'product_offer_id' => $productOffer->id,
                ], [
                    'quantity' => 0,
                ]);

                // Use same logic as before - add to existing quantity if exists
                if ($savedItem->exists) {
                    $savedItem->quantity += $quantity;
                } else {
                    $savedItem->quantity = $quantity;
                }

                $savedItem->save();
                $savedItems->push($savedItem);
                $processedCartItemIds->push($cartItem->id);
            } catch (Exception $e) {
            Log::error('Failed to add item to saved items', [
                'clinic_id' => $clinic->id,
                'cart_item_id' => $cartItem->id,
                'error' => $e->getMessage(),
            ]);

            continue;
        }
        }

        // Delete only successfully processed cart items
        if ($processedCartItemIds->isNotEmpty()) {
            CartItem::whereIn('id', $processedCartItemIds->toArray())->delete();
        }

        // Clear cart cache to ensure fresh data is loaded
        if ($clinic) {
            $this->getCartAction->forgetCache($clinic);
        }

        return $savedItems;
    }
}
