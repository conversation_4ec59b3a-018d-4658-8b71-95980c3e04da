<?php

declare(strict_types=1);

namespace App\Modules\SavedItems\Actions;

use App\Models\CartItem;
use App\Modules\Cart\Actions\GetCartAction;
use App\Modules\SavedItems\Models\SavedItem;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

final class AddToSavedItemsAction
{
    public function __construct(
        private readonly GetCartAction $getCartAction
    ) {}

    /**
     * Handle single cart item ID (backward compatibility).
     */
    public function handle(string $cartItemId): SavedItem
    {
        $savedItems = $this->handleBulk([$cartItemId]);

        return $savedItems->first();
    }

    /**
     * Handle multiple cart item IDs in a single transaction.
     */
    public function handleBulk(array $cartItemIds): Collection
    {
        return DB::transaction(function () use ($cartItemIds) {
            // Load all cart items with their relationships
            $cartItems = CartItem::with(['cart.clinic', 'productOffer'])
                ->whereIn('id', $cartItemIds)
                ->get();

            if ($cartItems->count() !== count($cartItemIds)) {
                throw new \InvalidArgumentException('One or more cart items not found');
            }

            // Group cart items by clinic to ensure all belong to the same clinic
            $clinicGroups = $cartItems->groupBy('cart.clinic_id');

            if ($clinicGroups->count() > 1) {
                throw new \InvalidArgumentException('All cart items must belong to the same clinic');
            }

            $clinic = $cartItems->first()->cart->clinic;
            $savedItems = collect();

            // Group cart items by product offer to accumulate quantities
            $productOfferGroups = $cartItems->groupBy('product_offer_id');

            foreach ($productOfferGroups as $productOfferId => $cartItemsGroup) {
                $totalQuantity = $cartItemsGroup->sum('quantity');
                $productOffer = $cartItemsGroup->first()->productOffer;

                // Find or create saved item
                $savedItem = SavedItem::firstOrNew([
                    'clinic_id' => $clinic->id,
                    'product_offer_id' => $productOfferId,
                ], [
                    'quantity' => 0,
                ]);

                // Accumulate quantity
                if ($savedItem->exists) {
                    $savedItem->quantity += $totalQuantity;
                } else {
                    $savedItem->quantity = $totalQuantity;
                }

                $savedItem->save();
                $savedItems->push($savedItem);
            }

            // Delete all cart items
            CartItem::whereIn('id', $cartItemIds)->delete();

            // Clear cart cache to ensure fresh data is loaded
            $this->getCartAction->forgetCache($clinic);

            return $savedItems;
        });
    }
}
