<?php

declare(strict_types=1);

namespace App\Modules\SavedItems\Actions;

use App\Modules\Cart\Actions\GetCartAction;
use App\Modules\SavedItems\Models\SavedItem;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

final class AddToCartFromSavedItemsAction
{
    public function __construct(
        private readonly GetCartAction $getCartAction
    ) {}

    /**
     * Handle multiple saved item IDs in a single transaction.
     */
    public function handle(array $savedItemIds): Collection
    {
        return DB::transaction(function () use ($savedItemIds) {
            // Load all saved items with their relationships
            $savedItems = SavedItem::with(['clinic.cart', 'productOffer'])
                ->whereIn('id', $savedItemIds)
                ->get();

            if ($savedItems->count() !== count($savedItemIds)) {
                throw new InvalidArgumentException('One or more saved items not found');
            }

            $clinic = $savedItems->first()->clinic;

            // Get or create cart
            $cart = $clinic->cart;
            if (! $cart) {
                $cart = $clinic->cart()->create();
            }

            // Process each saved item individually (same logic as before)
            foreach ($savedItems as $savedItem) {
                $productOffer = $savedItem->productOffer;
                $quantity = $savedItem->quantity;

                // Check if item already exists in cart
                $cartItem = $cart->items()->where('product_offer_id', $productOffer->id)->first();

                if ($cartItem) {
                    $cart->updateItem($productOffer, ['quantity' => $cartItem->quantity + $quantity]);
                } else {
                    $cart->addItem($productOffer, $quantity);
                }
            }

            // Delete all saved items
            SavedItem::whereIn('id', $savedItemIds)->delete();

            // Clear cart cache to ensure fresh data is loaded
            $this->getCartAction->forgetCache($clinic);

            return $savedItems;
        });
    }
}
