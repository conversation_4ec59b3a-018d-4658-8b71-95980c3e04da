<?php

declare(strict_types=1);

namespace App\Modules\SavedItems\Actions;

use App\Modules\Cart\Actions\GetCartAction;
use App\Modules\SavedItems\Models\SavedItem;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

final class AddToCartFromSavedItemsAction
{
    public function __construct(
        private readonly GetCartAction $getCartAction
    ) {}

    /**
     * Handle multiple saved item IDs in a single transaction.
     */
    public function handle(array $savedItemIds): Collection
    {
        return DB::transaction(function () use ($savedItemIds) {
            // Load all saved items with their relationships
            $savedItems = SavedItem::with(['clinic.cart', 'productOffer'])
                ->whereIn('id', $savedItemIds)
                ->get();

            if ($savedItems->count() !== count($savedItemIds)) {
                throw new \InvalidArgumentException('One or more saved items not found');
            }

            // Group saved items by clinic to ensure all belong to the same clinic
            $clinicGroups = $savedItems->groupBy('clinic_id');

            if ($clinicGroups->count() > 1) {
                throw new \InvalidArgumentException('All saved items must belong to the same clinic');
            }

            $clinic = $savedItems->first()->clinic;

            // Get or create cart
            $cart = $clinic->cart;
            if (! $cart) {
                $cart = $clinic->cart()->create();
            }

            // Group saved items by product offer to accumulate quantities
            $productOfferGroups = $savedItems->groupBy('product_offer_id');

            foreach ($productOfferGroups as $productOfferId => $savedItemsGroup) {
                $totalQuantity = $savedItemsGroup->sum('quantity');
                $productOffer = $savedItemsGroup->first()->productOffer;

                // Check if item already exists in cart
                $cartItem = $cart->items()->where('product_offer_id', $productOfferId)->first();

                if ($cartItem) {
                    $cart->updateItem($productOffer, ['quantity' => $cartItem->quantity + $totalQuantity]);
                } else {
                    $cart->addItem($productOffer, $totalQuantity);
                }
            }

            // Delete all saved items
            SavedItem::whereIn('id', $savedItemIds)->delete();

            // Clear cart cache to ensure fresh data is loaded
            $this->getCartAction->forgetCache($clinic);

            return $savedItems;
        });
    }
}
