<?php

declare(strict_types=1);

namespace App\Modules\SavedItems\Actions;

use App\Modules\Cart\Actions\GetCartAction;
use App\Modules\SavedItems\Models\SavedItem;
use Illuminate\Support\Collection;

final class AddToCartFromSavedItemsAction
{
    public function __construct(
        private readonly GetCartAction $getCartAction
    ) {}

    /**
     * Handle multiple saved item IDs.
     */
    public function handle(array $savedItemIds): Collection
    {
        // Load all saved items with their relationships
        $savedItems = SavedItem::with(['clinic.cart', 'productOffer'])
            ->whereIn('id', $savedItemIds)
            ->get();

        $processedSavedItems = collect();
        $processedSavedItemIds = collect();
        $clinic = null;
        $cart = null;

        // Process each saved item individually (same logic as before)
        foreach ($savedItems as $savedItem) {
            try {
                if (!$clinic) {
                    $clinic = $savedItem->clinic;
                    // Get or create cart
                    $cart = $clinic->cart;
                    if (! $cart) {
                        $cart = $clinic->cart()->create();
                    }
                }

                $productOffer = $savedItem->productOffer;
                $quantity = $savedItem->quantity;

                // Check if item already exists in cart
                $cartItem = $cart->items()->where('product_offer_id', $productOffer->id)->first();

                if ($cartItem) {
                    $cart->updateItem($productOffer, ['quantity' => $cartItem->quantity + $quantity]);
                } else {
                    $cart->addItem($productOffer, $quantity);
                }

                $processedSavedItems->push($savedItem);
                $processedSavedItemIds->push($savedItem->id);
            } catch (\Exception $e) {
                // Continue processing other items if one fails
                continue;
            }
        }

        // Delete only successfully processed saved items
        if ($processedSavedItemIds->isNotEmpty()) {
            SavedItem::whereIn('id', $processedSavedItemIds->toArray())->delete();
        }

        // Clear cart cache to ensure fresh data is loaded
        if ($clinic) {
            $this->getCartAction->forgetCache($clinic);
        }

        return $processedSavedItems;
    }
}
