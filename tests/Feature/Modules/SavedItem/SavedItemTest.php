<?php

declare(strict_types=1);

use App\Models\Cart;
use App\Models\CartItem;
use App\Models\Clinic;
use App\Models\User;
use App\Modules\Account\Models\ClinicAccount;
use App\Modules\SavedItems\Actions\AddToCartFromSavedItemsAction;
use App\Modules\SavedItems\Actions\AddToSavedItemsAction;
use App\Modules\SavedItems\Models\SavedItem;

describe('Actions', function () {
    it('creates a saved item and removes the cart item', function () {
        $cartItem = CartItem::factory()->create();
        $action = app(AddToSavedItemsAction::class);
        $action->handle($cartItem->id);

        $this->assertDatabaseHas('saved_items', [
            'clinic_id' => $cartItem->cart->clinic_id,
            'product_offer_id' => $cartItem->product_offer_id,
            'quantity' => $cartItem->quantity,
        ]);

        // Verify the cart item was actually soft deleted
        $this->assertSoftDeleted('cart_items', [
            'id' => $cartItem->id,
        ]);
    });

    it('adds to the saved item quantity', function () {
        $cartItem = CartItem::factory()->create([
            'quantity' => 2,
        ]);
        SavedItem::factory()->create([
            'clinic_id' => $cartItem->cart->clinic_id,
            'product_offer_id' => $cartItem->product_offer_id,
            'quantity' => 3,
        ]);
        $action = app(AddToSavedItemsAction::class);
        $action->handle($cartItem->id);

        $this->assertDatabaseHas('saved_items', [
            'clinic_id' => $cartItem->cart->clinic_id,
            'product_offer_id' => $cartItem->product_offer_id,
            'quantity' => 5,
        ]);
    });

    it('removes a saved item and adds it back to the cart', function () {
        $savedItem = SavedItem::factory()->create();
        $action = app(AddToCartFromSavedItemsAction::class);
        $action->handle($savedItem->id);

        $this->assertDatabaseHas('cart_items', [
            'product_offer_id' => $savedItem->product_offer_id,
            'quantity' => $savedItem->quantity,
        ]);

        $this->assertDatabaseMissing('saved_items', [
            'id' => $savedItem->id,
        ]);
    });

    it('adds a saved item to the cart and updates the saved item quantity', function () {
        $cartItem = CartItem::factory()->create([
            'quantity' => 10,
        ]);
        $savedItem = SavedItem::factory()->create([
            'clinic_id' => $cartItem->cart->clinic_id,
            'product_offer_id' => $cartItem->product_offer_id,
            'quantity' => 1,
        ]);
        $action = app(AddToCartFromSavedItemsAction::class);
        $action->handle($savedItem->id);

        $this->assertDatabaseHas('cart_items', [
            'product_offer_id' => $cartItem->product_offer_id,
            'quantity' => 11,
        ]);

        $this->assertDatabaseMissing('saved_items', [
            'id' => $savedItem->id,
        ]);
    });

    it('clears cart cache when moving items to saved items', function () {
        $cartItem = CartItem::factory()->create();
        $clinic = $cartItem->cart->clinic;

        // Create cache first
        $getCartAction = app(App\Modules\Cart\Actions\GetCartAction::class);
        $getCartAction->handle($clinic);

        // Verify cache exists
        $cacheKey = "clinic_cart_{$clinic->id}";
        $this->assertTrue(Cache::has($cacheKey));

        // Move item to saved items
        $action = app(AddToSavedItemsAction::class);
        $action->handle($cartItem->id);

        // Verify cache is cleared
        $this->assertFalse(Cache::has($cacheKey));
    });

    it('handles bulk cart items to saved items', function () {
        $cartItem1 = CartItem::factory()->create(['quantity' => 2]);
        $cartItem2 = CartItem::factory()->create([
            'cart_id' => $cartItem1->cart_id,
            'quantity' => 3,
        ]);
        $cartItem3 = CartItem::factory()->create([
            'cart_id' => $cartItem1->cart_id,
            'quantity' => 1,
        ]);

        $action = app(AddToSavedItemsAction::class);
        $savedItems = $action->handleBulk([$cartItem1->id, $cartItem2->id, $cartItem3->id]);

        expect($savedItems)->toHaveCount(3);

        $this->assertDatabaseHas('saved_items', [
            'clinic_id' => $cartItem1->cart->clinic_id,
            'product_offer_id' => $cartItem1->product_offer_id,
            'quantity' => 2,
        ]);

        $this->assertDatabaseHas('saved_items', [
            'clinic_id' => $cartItem2->cart->clinic_id,
            'product_offer_id' => $cartItem2->product_offer_id,
            'quantity' => 3,
        ]);

        $this->assertDatabaseHas('saved_items', [
            'clinic_id' => $cartItem3->cart->clinic_id,
            'product_offer_id' => $cartItem3->product_offer_id,
            'quantity' => 1,
        ]);

        // Verify all cart items were deleted
        $this->assertSoftDeleted('cart_items', ['id' => $cartItem1->id]);
        $this->assertSoftDeleted('cart_items', ['id' => $cartItem2->id]);
        $this->assertSoftDeleted('cart_items', ['id' => $cartItem3->id]);
    });

    it('accumulates quantities for same product offers in bulk operation', function () {
        $cart = Cart::factory()->create();
        $productOffer = \App\Models\ProductOffer::factory()->create();

        $cartItem1 = CartItem::factory()->create([
            'cart_id' => $cart->id,
            'product_offer_id' => $productOffer->id,
            'quantity' => 2,
        ]);
        $cartItem2 = CartItem::factory()->create([
            'cart_id' => $cart->id,
            'product_offer_id' => $productOffer->id,
            'quantity' => 3,
        ]);

        $action = app(AddToSavedItemsAction::class);
        $savedItems = $action->handleBulk([$cartItem1->id, $cartItem2->id]);

        expect($savedItems)->toHaveCount(1);

        $this->assertDatabaseHas('saved_items', [
            'clinic_id' => $cart->clinic_id,
            'product_offer_id' => $productOffer->id,
            'quantity' => 5, // 2 + 3
        ]);
    });

    it('handles bulk saved items to cart', function () {
        $clinic = Clinic::factory()->create();
        $cart = Cart::factory()->create(['clinic_id' => $clinic->id]);

        $savedItem1 = SavedItem::factory()->create(['clinic_id' => $clinic->id, 'quantity' => 2]);
        $savedItem2 = SavedItem::factory()->create(['clinic_id' => $clinic->id, 'quantity' => 3]);
        $savedItem3 = SavedItem::factory()->create(['clinic_id' => $clinic->id, 'quantity' => 1]);

        $action = app(AddToCartFromSavedItemsAction::class);
        $savedItems = $action->handleBulk([$savedItem1->id, $savedItem2->id, $savedItem3->id]);

        expect($savedItems)->toHaveCount(3);

        $this->assertDatabaseHas('cart_items', [
            'cart_id' => $cart->id,
            'product_offer_id' => $savedItem1->product_offer_id,
            'quantity' => 2,
        ]);

        $this->assertDatabaseHas('cart_items', [
            'cart_id' => $cart->id,
            'product_offer_id' => $savedItem2->product_offer_id,
            'quantity' => 3,
        ]);

        $this->assertDatabaseHas('cart_items', [
            'cart_id' => $cart->id,
            'product_offer_id' => $savedItem3->product_offer_id,
            'quantity' => 1,
        ]);

        // Verify all saved items were deleted
        $this->assertDatabaseMissing('saved_items', ['id' => $savedItem1->id]);
        $this->assertDatabaseMissing('saved_items', ['id' => $savedItem2->id]);
        $this->assertDatabaseMissing('saved_items', ['id' => $savedItem3->id]);
    });

    it('throws exception when bulk cart items belong to different clinics', function () {
        $cartItem1 = CartItem::factory()->create();
        $cartItem2 = CartItem::factory()->create(); // Different clinic

        $action = app(AddToSavedItemsAction::class);

        expect(fn() => $action->handleBulk([$cartItem1->id, $cartItem2->id]))
            ->toThrow(\InvalidArgumentException::class, 'All cart items must belong to the same clinic');
    });

    it('throws exception when bulk saved items belong to different clinics', function () {
        $savedItem1 = SavedItem::factory()->create();
        $savedItem2 = SavedItem::factory()->create(); // Different clinic

        $action = app(AddToCartFromSavedItemsAction::class);

        expect(fn() => $action->handleBulk([$savedItem1->id, $savedItem2->id]))
            ->toThrow(\InvalidArgumentException::class, 'All saved items must belong to the same clinic');
    });
});

// Api endpoints
describe('Api endpoints', function () {
    beforeEach(function () {
        $this->user = User::factory()->create();
        $this->account = ClinicAccount::factory()->create();
        $this->user->update([
            'account_id' => $this->account->id,
        ]);
        $this->clinic = Clinic::factory()->create([
            'clinic_account_id' => $this->account->id,
        ]);
        $this->cart = Cart::factory()->create([
            'clinic_id' => $this->clinic->id,
        ]);
        $this->cartItem = CartItem::factory()->create([
            'cart_id' => $this->cart->id,
        ]);
    });

    it('user cannot add saved item to another clinic', function () {
        $user = User::factory()->create();
        $account = ClinicAccount::factory()->create();
        $user->update([
            'account_id' => $account->id,
        ]);
        $clinic = Clinic::factory()->create([
            'clinic_account_id' => $account->id,
        ]);

        $response = $this->actingAs($user)
            ->withHeader('highfive-clinic', $clinic->id)
            ->postJson('/api/saved-items/add', [
                'cart_item_id' => $this->cartItem->id,
            ]);

        $response->assertStatus(403);
    });

    it('user can see saved items', function () {
        SavedItem::factory()->count(3)->create([
            'clinic_id' => $this->clinic->id,
        ]);

        $response = $this->actingAs($this->user)
            ->withHeader('highfive-clinic', $this->clinic->id)
            ->getJson('/api/saved-items');

        $response->assertStatus(200);

        $response->assertJsonStructure([
            'savedItems' => [
                '*' => [
                    'id',
                    'clinicId',
                    'productOfferId',
                    'quantity',
                    'createdAt',
                    'productOffer' => [
                        'id',
                        'vendor' => [
                            'id',
                            'name',
                            'imageUrl',
                        ],
                        'vendorSku',
                        'price',
                        'clinicPrice',
                        'stockStatus',
                        'increments',
                        'isRecommended',
                    ],
                ],
            ],
        ]);
    });

    it('user can add saved item to their clinic', function () {
        $response = $this->actingAs($this->user)
            ->withHeader('highfive-clinic', $this->clinic->id)
            ->postJson('/api/saved-items/add', [
                'cart_item_id' => $this->cartItem->id,
            ]);

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'id',
            'clinicId',
            'productOfferId',
            'quantity',
            'createdAt',
            'productOffer' => [
                'id',
                'vendor' => [
                    'id',
                    'name',
                    'imageUrl',
                ],
                'vendorSku',
                'price',
                'clinicPrice',
                'stockStatus',
                'increments',
                'isRecommended',
            ],
        ]);
    });

    it('user remove cart item and add it to saved items', function () {
        $response = $this->actingAs($this->user)
            ->withHeader('highfive-clinic', $this->clinic->id)
            ->postJson('/api/saved-items/add', [
                'cart_item_id' => $this->cartItem->id,
            ]);

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'id',
            'clinicId',
            'productOfferId',
            'quantity',
            'createdAt',
            'productOffer' => [
                'id',
                'vendor' => [
                    'id',
                    'name',
                    'imageUrl',
                ],
                'vendorSku',
                'price',
                'clinicPrice',
                'stockStatus',
                'increments',
                'isRecommended',
            ],
        ]);

        // Verify the cart item was actually soft deleted
        $this->assertSoftDeleted('cart_items', [
            'id' => $this->cartItem->id,
        ]);
    });

    it('user can remove saved item', function () {
        $savedItem = SavedItem::factory()->create([
            'clinic_id' => $this->clinic->id,
            'product_offer_id' => $this->cartItem->product_offer_id,
        ]);

        $response = $this->actingAs($this->user)
            ->withHeader('highfive-clinic', $this->clinic->id)
            ->deleteJson('/api/saved-items/remove', [
                'saved_item_id' => $savedItem->id,
            ]);

        $response->assertStatus(204);
    });

    it('can add multiple cart items to saved items via API', function () {
        $cartItem2 = CartItem::factory()->create([
            'cart_id' => $this->cart->id,
        ]);
        $cartItem3 = CartItem::factory()->create([
            'cart_id' => $this->cart->id,
        ]);

        $response = $this->actingAs($this->user)
            ->withHeader('highfive-clinic', $this->clinic->id)
            ->postJson('/api/saved-items/add', [
                'cart_item_ids' => [$this->cartItem->id, $cartItem2->id, $cartItem3->id],
            ]);

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'saved_items' => [
                '*' => [
                    'id',
                    'clinic_id',
                    'product_offer_id',
                    'quantity',
                    'created_at',
                    'product_offer',
                ]
            ],
            'count'
        ]);

        expect($response->json('count'))->toBe(3);
    });

    it('can remove multiple saved items via API', function () {
        $savedItem1 = SavedItem::factory()->create([
            'clinic_id' => $this->clinic->id,
        ]);
        $savedItem2 = SavedItem::factory()->create([
            'clinic_id' => $this->clinic->id,
        ]);
        $savedItem3 = SavedItem::factory()->create([
            'clinic_id' => $this->clinic->id,
        ]);

        $response = $this->actingAs($this->user)
            ->withHeader('highfive-clinic', $this->clinic->id)
            ->deleteJson('/api/saved-items/remove', [
                'saved_item_ids' => [$savedItem1->id, $savedItem2->id, $savedItem3->id],
            ]);

        $response->assertStatus(204);

        $this->assertDatabaseMissing('saved_items', ['id' => $savedItem1->id]);
        $this->assertDatabaseMissing('saved_items', ['id' => $savedItem2->id]);
        $this->assertDatabaseMissing('saved_items', ['id' => $savedItem3->id]);
    });

    it('validates cart_item_ids array format', function () {
        $response = $this->actingAs($this->user)
            ->withHeader('highfive-clinic', $this->clinic->id)
            ->postJson('/api/saved-items/add', [
                'cart_item_ids' => 'not-an-array',
            ]);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['cart_item_ids']);
    });

    it('validates saved_item_ids array format', function () {
        $response = $this->actingAs($this->user)
            ->withHeader('highfive-clinic', $this->clinic->id)
            ->deleteJson('/api/saved-items/remove', [
                'saved_item_ids' => 'not-an-array',
            ]);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['saved_item_ids']);
    });

    it('requires either cart_item_id or cart_item_ids', function () {
        $response = $this->actingAs($this->user)
            ->withHeader('highfive-clinic', $this->clinic->id)
            ->postJson('/api/saved-items/add', []);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['cart_item_id', 'cart_item_ids']);
    });

    it('requires either saved_item_id or saved_item_ids', function () {
        $response = $this->actingAs($this->user)
            ->withHeader('highfive-clinic', $this->clinic->id)
            ->deleteJson('/api/saved-items/remove', []);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['saved_item_id', 'saved_item_ids']);
    });

    it('maintains backward compatibility with single cart_item_id', function () {
        $response = $this->actingAs($this->user)
            ->withHeader('highfive-clinic', $this->clinic->id)
            ->postJson('/api/saved-items/add', [
                'cart_item_id' => $this->cartItem->id,
            ]);

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'id',
            'clinic_id',
            'product_offer_id',
            'quantity',
            'created_at',
            'product_offer',
        ]);
    });

    it('maintains backward compatibility with single saved_item_id', function () {
        $savedItem = SavedItem::factory()->create([
            'clinic_id' => $this->clinic->id,
        ]);

        $response = $this->actingAs($this->user)
            ->withHeader('highfive-clinic', $this->clinic->id)
            ->deleteJson('/api/saved-items/remove', [
                'saved_item_id' => $savedItem->id,
            ]);

        $response->assertStatus(204);
    });
});
